# Diagrama de Secuencia: Asignación de Miembros a Proyecto

Este diagrama muestra el flujo completo para asignar un miembro a un proyecto en el sistema de gestión de proyectos Scrum.

## Descripción del Flujo

El proceso de asignación de miembros a proyecto involucra los siguientes pasos:

1. **Autenticación**: Verificación de que el usuario es administrador
2. **Carga de datos**: Obtención de usuarios disponibles y proyecto
3. **Validación**: Verificación de datos de entrada y permisos
4. **Asignación**: Creación del registro de miembro en el proyecto
5. **Persistencia**: Almacenamiento en base de datos con índices
6. **Respuesta**: Confirmación de éxito o manejo de errores

## Diagrama de Secuencia

```mermaid
sequenceDiagram
    participant Admin as 👤 Administrador
    participant UI as 🖥️ AssignProjectForm
    participant AuthAPI as 🔐 /api/session
    participant UsersAPI as 👥 /api/admin/users
    participant MembersAPI as 🔗 /api/projects/members
    participant ProjectService as ⚙️ ProjectService
    participant UserModel as 👤 UserModel
    participant ProjectModel as 📁 ProjectModel
    participant KV as 🗄️ Base de Datos (KV)

    Note over Admin, KV: Inicio del proceso de asignación

    Admin->>UI: Abre modal de asignación
    UI->>AuthAPI: Verificar sesión actual
    AuthAPI->>KV: Obtener sesión por sessionId
    KV-->>AuthAPI: Datos de sesión
    AuthAPI-->>UI: Session { role: ADMIN }

    alt Usuario no autenticado
        AuthAPI-->>UI: Error 401 - No autenticado
        UI-->>Admin: Redirigir a login
    else Usuario no es admin
        AuthAPI-->>UI: Error 403 - No autorizado
        UI-->>Admin: Mostrar error de permisos
    end

    Note over UI, UsersAPI: Carga de usuarios disponibles

    UI->>UsersAPI: GET /api/admin/users
    UsersAPI->>KV: Verificar sesión admin
    KV-->>UsersAPI: Sesión válida
    UsersAPI->>UserModel: getAllUsers()
    UserModel->>KV: Obtener todos los usuarios
    KV-->>UserModel: Lista de usuarios
    UserModel-->>UsersAPI: Usuarios filtrados
    UsersAPI-->>UI: Lista de usuarios no admin

    UI->>UI: Filtrar usuarios ya asignados
    UI-->>Admin: Mostrar formulario con usuarios disponibles

    Note over Admin, KV: Proceso de asignación

    Admin->>UI: Selecciona usuario y rol
    Admin->>UI: Envía formulario

    UI->>UI: Validar datos localmente
    alt Datos inválidos
        UI-->>Admin: Mostrar errores de validación
    end

    UI->>MembersAPI: POST /api/projects/members<br/>{ userId, projectId, role }

    Note over MembersAPI, KV: Validaciones del servidor

    MembersAPI->>KV: Verificar sesión
    KV-->>MembersAPI: Session { role: ADMIN }

    alt Usuario no es admin
        MembersAPI-->>UI: Error 403 - No autorizado
        UI-->>Admin: Mostrar error
    end

    MembersAPI->>MembersAPI: Validar con ProjectMemberSchema
    alt Datos inválidos
        MembersAPI-->>UI: Error 400 - Datos inválidos
        UI-->>Admin: Mostrar error de validación
    end

    MembersAPI->>ProjectModel: getProjectById(projectId)
    ProjectModel->>KV: Buscar proyecto
    KV-->>ProjectModel: Datos del proyecto
    ProjectModel-->>MembersAPI: Proyecto encontrado

    alt Proyecto no existe
        MembersAPI-->>UI: Error 404 - Proyecto no encontrado
        UI-->>Admin: Mostrar error
    end

    MembersAPI->>MembersAPI: Verificar si usuario ya asignado
    alt Usuario ya asignado
        MembersAPI-->>UI: Error 400 - Usuario ya asignado
        UI-->>Admin: Mostrar error
    end

    Note over MembersAPI, KV: Verificación de compatibilidad de roles

    MembersAPI->>UserModel: getUserById(userId)
    UserModel->>KV: Buscar usuario
    KV-->>UserModel: Datos del usuario
    UserModel-->>MembersAPI: Usuario encontrado

    MembersAPI->>ProjectService: addProjectMember(memberData)
    ProjectService->>ProjectService: Verificar compatibilidad de roles
    alt Roles incompatibles
        ProjectService-->>MembersAPI: Error - Roles incompatibles
        MembersAPI-->>UI: Error 400 - Roles incompatibles
        UI-->>Admin: Mostrar error
    end

    Note over ProjectService, KV: Creación del miembro

    ProjectService->>ProjectModel: addProjectMember(memberData)
    ProjectModel->>ProjectModel: createModel(memberData)
    ProjectModel->>KV: Guardar miembro
    KV-->>ProjectModel: Miembro guardado

    Note over ProjectModel, KV: Creación de índices

    ProjectModel->>KV: Crear índice by_user
    KV-->>ProjectModel: Índice creado
    ProjectModel->>KV: Crear índice by_project
    KV-->>ProjectModel: Índice creado

    ProjectModel-->>ProjectService: Miembro creado
    ProjectService-->>MembersAPI: Miembro asignado

    Note over MembersAPI, Admin: Respuesta exitosa

    MembersAPI-->>UI: Success 201 - Usuario asignado exitosamente
    UI->>UI: Cerrar modal
    UI->>UI: Actualizar lista de miembros
    UI-->>Admin: Mostrar mensaje de éxito

    Note over Admin, KV: Fin del proceso
```

## Componentes Involucrados

### Frontend
- **AssignProjectForm**: Componente React/Preact que maneja el formulario de asignación
- **Modal**: Contenedor del formulario de asignación

### Backend APIs
- **GET /api/session**: Verificación de autenticación
- **GET /api/admin/users**: Obtención de usuarios disponibles
- **POST /api/projects/members**: Endpoint principal para asignación

### Servicios
- **ProjectService**: Lógica de negocio para gestión de proyectos
- **UserModel**: Modelo de datos de usuarios
- **ProjectModel**: Modelo de datos de proyectos

### Base de Datos
- **Deno KV**: Almacenamiento persistente con índices para búsqueda rápida

## Validaciones Implementadas

1. **Autenticación**: Solo usuarios administradores pueden asignar miembros
2. **Existencia**: Verificación de que el proyecto y usuario existen
3. **Duplicación**: Prevención de asignaciones duplicadas
4. **Compatibilidad de roles**: Verificación de que el rol del usuario es compatible con el rol del proyecto
5. **Esquema de datos**: Validación con Zod de la estructura de datos

## Manejo de Errores

- **401 Unauthorized**: Usuario no autenticado
- **403 Forbidden**: Usuario sin permisos de administrador
- **400 Bad Request**: Datos inválidos o usuario ya asignado
- **404 Not Found**: Proyecto no encontrado
- **500 Internal Server Error**: Errores del servidor o base de datos

## Tecnologías Utilizadas

- **Frontend**: Deno Fresh + Preact
- **Backend**: Deno + Fresh API Routes
- **Base de Datos**: Deno KV (Key-Value Store)
- **Validación**: Zod schemas
- **Autenticación**: Session-based con cookies HTTP-only